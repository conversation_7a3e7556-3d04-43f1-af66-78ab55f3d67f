<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    h3 {
      margin-top: 0;
      color: #333;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 15px;
      box-sizing: border-box;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      width: 100%;
    }
    button:hover {
      background: #0056b3;
    }
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      text-align: center;
      font-size: 14px;
    }
    .success {
      background: #d4edda;
      color: #155724;
    }
    .error {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <h3>Bolt.new Error Assistant</h3>
  
  <label for="apiUrl">API URL:</label>
  <input type="url" id="apiUrl" placeholder="https://gab.ai/v1">
  
  <label for="apiKey">API Key:</label>
  <input type="password" id="apiKey" placeholder="Your API key">
  
  <button id="save">Save Settings</button>
  
  <div id="status"></div>

  <script src="options.js"></script>
</body>
</html>
