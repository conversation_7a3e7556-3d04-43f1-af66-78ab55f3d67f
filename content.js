class BoltErrorAssistant {
  constructor() {
    this.errors = [];
    this.errorButton = null;
    this.isObserving = false;
    this.existingErrors = new Set(); // Track errors that existed before we started observing
    this.init();
  }

  async init() {
    // Wait for the page to load and find the target button
    this.waitForChatWindow();
    // Record existing errors before starting to observe
    this.recordExistingErrors();
    this.startErrorObserver();
  }

  waitForChatWindow() {
    const checkForButton = () => {
      const targetButton = document.querySelector('button[data-state="closed"] span.i-ph\\:flask-duotone');
      if (targetButton && targetButton.closest('button')) {
        this.createErrorButton(targetButton.closest('button'));
      } else {
        setTimeout(checkForButton, 1000);
      }
    };
    checkForButton();
  }

  recordExistingErrors() {
    // Only record existing "Potential problem detected" errors
    const potentialProblems = document.querySelectorAll('.text-lg.i-ph\\:warning.text-bolt-elements-icon-error');
    potentialProblems.forEach(element => {
      const errorContainer = element.closest('[class*="error"], [class*="warning"], [class*="problem"]') ||
                            element.parentElement;
      if (errorContainer && errorContainer.textContent.includes('Potential problem detected')) {
        const errorText = errorContainer.textContent || errorContainer.innerText;
        if (errorText) {
          this.existingErrors.add(errorText);
        }
      }
    });

    // Also check for any elements containing "Potential problem detected" text
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      if (element.textContent && element.textContent.includes('Potential problem detected')) {
        const errorText = element.textContent || element.innerText;
        if (errorText) {
          this.existingErrors.add(errorText);
        }
      }
    });
  }

  createErrorButton(referenceButton) {
    if (this.errorButton) return; // Already created

    const errorButton = document.createElement('button');
    errorButton.className = referenceButton.className;
    errorButton.type = 'button';
    errorButton.setAttribute('data-state', 'closed');
    errorButton.setAttribute('title', 'Error Assistant');

    errorButton.innerHTML = `
      <span class="bolt-error-icon flex items-center justify-center h-4 text-base leading-4">⚠️</span>
      <span class="truncate bolt-error-count">0</span>
    `;

    errorButton.addEventListener('click', () => this.handleErrorClick());

    // Insert after the reference button
    referenceButton.parentNode.insertBefore(errorButton, referenceButton.nextSibling);
    this.errorButton = errorButton;
  }

  startErrorObserver() {
    if (this.isObserving) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Only check for "Potential problem detected" errors
            const potentialProblemElement = node.querySelector('.text-lg.i-ph\\:warning.text-bolt-elements-icon-error') ||
                                          (node.classList && node.classList.contains('text-lg') &&
                                           node.classList.contains('i-ph:warning') &&
                                           node.classList.contains('text-bolt-elements-icon-error') ? node : null);

            if (potentialProblemElement) {
              // Find the parent container that contains the full error message
              const errorContainer = potentialProblemElement.closest('[class*="error"], [class*="warning"], [class*="problem"]') ||
                                    potentialProblemElement.parentElement;
              if (errorContainer && errorContainer.textContent.includes('Potential problem detected')) {
                this.captureError(errorContainer);
              }
            }

            // Also check for elements containing "Potential problem detected" text
            if (node.textContent && node.textContent.includes('Potential problem detected')) {
              this.captureError(node);
            }
          }
        });

        // Check for text changes that might indicate error resolution
        mutation.removedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.textContent &&
              node.textContent.includes('Potential problem detected')) {
            this.checkForResolvedErrors();
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.isObserving = true;
  }

  captureError(errorElement) {
    const errorText = errorElement.textContent || errorElement.innerText;
    // Only capture errors that are new (not existing when we started) and not already captured
    // and contain "Potential problem detected"
    if (errorText && errorText.includes('Potential problem detected') &&
        !this.existingErrors.has(errorText) && !this.errors.includes(errorText)) {
      this.errors.push(errorText);
      this.updateErrorButton();
      console.log('New "Potential problem detected" error captured:', errorText);

      // Show notification for new error
      this.showNotification(`New problem detected! (${this.errors.length} total)`, 'info');
    }
  }

  checkForResolvedErrors() {
    // Check if any of our captured errors are no longer present on the page
    const currentPotentialProblems = new Set();

    // Get all current "Potential problem detected" elements
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      if (element.textContent && element.textContent.includes('Potential problem detected')) {
        currentPotentialProblems.add(element.textContent || element.innerText);
      }
    });

    // Check if any of our errors are resolved
    const resolvedErrors = this.errors.filter(error => !currentPotentialProblems.has(error));

    if (resolvedErrors.length > 0) {
      // Remove resolved errors from our list
      this.errors = this.errors.filter(error => currentPotentialProblems.has(error));
      this.updateErrorButton();

      console.log('Errors resolved:', resolvedErrors);
      this.showNotification(`${resolvedErrors.length} problem(s) resolved!`, 'success');
    }
  }

  updateErrorButton() {
    if (!this.errorButton) return;

    const countSpan = this.errorButton.querySelector('.bolt-error-count');
    const iconSpan = this.errorButton.querySelector('.bolt-error-icon');
    
    if (countSpan) {
      countSpan.textContent = this.errors.length;
    }
    
    if (iconSpan && this.errors.length > 0) {
      iconSpan.style.color = '#ff4444';
      iconSpan.style.animation = 'pulse 1s infinite';
    }
  }

  async handleErrorClick() {
    if (this.errors.length === 0) {
      this.showNotification('No new errors captured yet. Errors that existed before the page loaded are not captured.', 'info');
      return;
    }

    const latestError = this.errors[this.errors.length - 1];

    // Show loading notification
    this.showNotification('Processing error with Bolt...', 'loading');

    // Copy error to clipboard
    try {
      await navigator.clipboard.writeText(latestError);
    } catch (err) {
      console.warn('Could not copy to clipboard:', err);
    }

    // Toggle to discuss mode
    this.toggleDiscussMode();

    // Wait a bit then paste the error and submit to Bolt
    setTimeout(async () => {
      await this.submitErrorToBolt(latestError);
    }, 500);
  }

  showNotification(message, type = 'info') {
    // Remove existing notification if any
    const existing = document.querySelector('.bolt-error-notification');
    if (existing) {
      existing.remove();
    }

    const notification = document.createElement('div');
    notification.className = `bolt-error-notification bolt-error-notification-${type}`;
    notification.textContent = message;

    // Style the notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '600',
      fontSize: '14px',
      zIndex: '10001',
      maxWidth: '300px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
      animation: 'slideInRight 0.3s ease-out'
    });

    // Set background color based on type
    switch (type) {
      case 'success':
        notification.style.background = 'linear-gradient(135deg, #10b981, #059669)';
        break;
      case 'error':
        notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
        break;
      case 'loading':
        notification.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
        break;
      default:
        notification.style.background = 'linear-gradient(135deg, #6b7280, #4b5563)';
    }

    document.body.appendChild(notification);

    // Auto remove after 3 seconds (except for loading)
    if (type !== 'loading') {
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideOutRight 0.3s ease-in';
          setTimeout(() => notification.remove(), 300);
        }
      }, 3000);
    }

    return notification;
  }

  toggleDiscussMode() {
    const discussButton = document.querySelector('button[aria-label="Toggle mode"]');
    if (discussButton) {
      discussButton.click();
    }
  }

  async submitErrorToBolt(errorText) {
    // Find and fill the textarea using the correct class
    const textarea = document.querySelector('.w-full.pl-4.pt-4.pr-16.focus\\:outline-none.resize-none.text-bolt-elements-textPrimary.placeholder-bolt-elements-textTertiary.bg-transparent.text-sm') ||
                    document.querySelector('textarea[placeholder*="chat"]') ||
                    document.querySelector('textarea[placeholder*="message"]');

    if (!textarea) {
      this.showNotification('Could not find chat textarea', 'error');
      console.error('Textarea not found. Available textareas:', document.querySelectorAll('textarea'));
      return;
    }

    console.log('Found textarea:', textarea);
    console.log('Textarea classes:', textarea.className);

    // Paste the error text
    textarea.value = errorText;
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.dispatchEvent(new Event('change', { bubbles: true }));
    textarea.focus();

    // Wait a moment for the UI to update
    await new Promise(resolve => setTimeout(resolve, 500));

    // Find and click the submit button (enter or arrow)
    const submitButton = document.querySelector('.i-ph\\:arrow-right') ||
                        document.querySelector('button .i-ph\\:arrow-right') ||
                        document.querySelector('[class*="i-ph:arrow-right"]') ||
                        document.querySelector('button[type="submit"]') ||
                        document.querySelector('button[aria-label*="send"]') ||
                        document.querySelector('button[aria-label*="submit"]');

    console.log('Submit button found:', submitButton);

    if (submitButton) {
      submitButton.click();
      console.log('Submitted error to Bolt via button click');
      this.showNotification('Error submitted to Bolt, waiting for response...', 'loading');

      // Wait for Bolt's response
      this.waitForBoltResponse(errorText);
    } else {
      console.log('Submit button not found, trying Enter key');
      console.log('Available buttons:', document.querySelectorAll('button'));

      // Fallback: simulate Enter key press
      const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        which: 13,
        bubbles: true
      });
      textarea.dispatchEvent(enterEvent);

      // Also try keyup event
      const enterUpEvent = new KeyboardEvent('keyup', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        which: 13,
        bubbles: true
      });
      textarea.dispatchEvent(enterUpEvent);

      console.log('Sent Enter key events to submit error to Bolt');
      this.showNotification('Error submitted to Bolt, waiting for response...', 'loading');

      // Wait for Bolt's response
      this.waitForBoltResponse(errorText);
    }
  }

  async waitForBoltResponse(originalError) {
    this.showNotification('Waiting for Bolt\'s response...', 'loading');

    // Keep track of existing markdown content to detect new responses
    const existingMarkdownElements = document.querySelectorAll('._MarkdownContent_19116_1');
    const existingCount = existingMarkdownElements.length;

    let attempts = 0;
    const maxAttempts = 30; // Wait up to 30 seconds

    const checkForResponse = async () => {
      attempts++;

      // Look for new markdown content
      const currentMarkdownElements = document.querySelectorAll('._MarkdownContent_19116_1');

      if (currentMarkdownElements.length > existingCount) {
        // New content appeared, get the last one
        const lastMarkdown = currentMarkdownElements[currentMarkdownElements.length - 1];

        // Check if it's fully loaded (has substantial content and looks like a plan)
        const content = lastMarkdown.textContent || lastMarkdown.innerText;

        if (content && content.length > 50 &&
            (content.includes('step') || content.includes('plan') || content.includes('solution') ||
             content.includes('1.') || content.includes('•') || content.includes('-'))) {
          console.log('Bolt response received:', content.substring(0, 200) + '...');

          // Remove loading notification
          const loadingNotification = document.querySelector('.bolt-error-notification-loading');
          if (loadingNotification) {
            loadingNotification.remove();
          }

          this.showNotification('Bolt response received! Opening AI assistant...', 'success');

          // Show AI assistant popup with both original error and Bolt's response
          setTimeout(() => {
            this.showAIAssistantPopup(originalError, content);
          }, 1000);
          return;
        }
      }

      // Continue waiting if we haven't reached max attempts
      if (attempts < maxAttempts) {
        setTimeout(checkForResponse, 1000);
      } else {
        console.log('Timeout waiting for Bolt response, proceeding with original error only');

        // Remove loading notification
        const loadingNotification = document.querySelector('.bolt-error-notification-loading');
        if (loadingNotification) {
          loadingNotification.remove();
        }

        this.showNotification('Timeout waiting for Bolt response', 'error');
        this.showAIAssistantPopup(originalError);
      }
    };

    // Start checking after a short delay
    setTimeout(checkForResponse, 2000);
  }

  pasteErrorToChat(errorText) {
    const textarea = document.querySelector('.w-full.pl-4.pt-4.pr-16.focus\\:outline-none.resize-none.text-bolt-elements-textPrimary.placeholder-bolt-elements-textTertiary.bg-transparent.text-sm') ||
                    document.querySelector('textarea[placeholder*="chat"]') ||
                    document.querySelector('textarea[placeholder*="message"]');

    if (textarea) {
      textarea.value = errorText;
      textarea.dispatchEvent(new Event('input', { bubbles: true }));
      textarea.focus();
    }
  }

  async showAIAssistantPopup(errorText, boltResponse = null) {
    // Create popup overlay
    const overlay = document.createElement('div');
    overlay.className = 'bolt-error-popup-overlay';
    
    const popup = document.createElement('div');
    popup.className = 'bolt-error-popup';
    
    const boltSection = boltResponse ? `
        <label>Bolt's Analysis:</label>
        <textarea class="bolt-error-input" readonly style="min-height: 120px; margin-bottom: 16px;">${boltResponse}</textarea>
    ` : '';

    popup.innerHTML = `
      <div class="bolt-error-popup-header">
        <h3>AI Error Assistant</h3>
        <button class="bolt-error-popup-close">×</button>
      </div>
      <div class="bolt-error-popup-content">
        <label>Original Error:</label>
        <textarea class="bolt-error-input" readonly>${errorText}</textarea>

        ${boltSection}

        <label>AI Response:</label>
        <div class="bolt-error-response-container">
          <div class="bolt-error-loading">Getting AI assistance...</div>
          <textarea class="bolt-error-response" readonly style="display: none;"></textarea>
        </div>

        <div class="bolt-error-popup-actions">
          <button class="bolt-error-retry">Retry</button>
          <button class="bolt-error-copy-response">Copy Response</button>
        </div>
      </div>
    `;

    overlay.appendChild(popup);
    document.body.appendChild(overlay);

    // Event listeners
    overlay.querySelector('.bolt-error-popup-close').addEventListener('click', () => {
      document.body.removeChild(overlay);
    });

    overlay.querySelector('.bolt-error-retry').addEventListener('click', () => {
      const button = overlay.querySelector('.bolt-error-retry');
      const originalText = button.textContent;
      button.textContent = 'Retrying...';
      button.disabled = true;

      this.getAIResponse(errorText, overlay, boltResponse).finally(() => {
        button.textContent = originalText;
        button.disabled = false;
      });
    });

    overlay.querySelector('.bolt-error-copy-response').addEventListener('click', async () => {
      const response = overlay.querySelector('.bolt-error-response').value;
      const button = overlay.querySelector('.bolt-error-copy-response');
      const originalText = button.textContent;

      try {
        await navigator.clipboard.writeText(response);
        button.textContent = '✓ Copied!';
        button.style.background = '#10b981';
        button.style.color = 'white';
        button.style.borderColor = '#10b981';

        setTimeout(() => {
          button.textContent = originalText;
          button.style.background = '#f8fafc';
          button.style.color = '#475569';
          button.style.borderColor = '#e2e8f0';
        }, 2000);
      } catch (err) {
        button.textContent = 'Copy failed';
        setTimeout(() => {
          button.textContent = originalText;
        }, 2000);
      }
    });

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay);
      }
    });

    // Get AI response
    this.getAIResponse(errorText, overlay, boltResponse);
  }

  async getAIResponse(errorText, overlay, boltResponse = null) {
    const loadingDiv = overlay.querySelector('.bolt-error-loading');
    const responseTextarea = overlay.querySelector('.bolt-error-response');

    loadingDiv.style.display = 'block';
    loadingDiv.textContent = 'Getting AI assistance...';
    responseTextarea.style.display = 'none';

    try {
      // Get API settings from storage
      const result = await chrome.storage.local.get(['apiUrl', 'apiKey']);
      const apiUrl = result.apiUrl || 'https://gab.ai/v1';
      const apiKey = result.apiKey || 'gab_19b4be24e684bee9ea215ff0a2593100';

      if (!apiKey) {
        throw new Error('API key not configured. Please set your API key in the extension options.');
      }

      // Use background script to make API request (bypasses CORS)
      const requestData = {
        action: 'makeApiRequest',
        url: `${apiUrl}/chat/completions`,
        options: {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: 'gab-ai',
            messages: [
              {
                role: 'system',
                content: 'You are an expert developer assistant. Analyze the error message and provide a detailed explanation of the problem and step-by-step solution. If Bolt.new has already provided an analysis, build upon it and provide additional insights.'
              },
              {
                role: 'user',
                content: boltResponse
                  ? `I encountered this error in bolt.new: ${errorText}\n\nBolt.new provided this analysis:\n${boltResponse}\n\nPlease provide additional insights, alternative solutions, or improvements to this analysis.`
                  : `I encountered this error in bolt.new: ${errorText}\n\nPlease explain what this error means and provide a detailed solution with steps to fix it.`
              }
            ],
            max_tokens: 1000
          })
        }
      };

      // Send message to background script
      console.log('Sending API request to background script:', requestData);
      const apiResult = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(requestData, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Chrome runtime error:', chrome.runtime.lastError);
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            console.log('Background script response:', response);
            resolve(response);
          }
        });
      });

      if (!apiResult.success) {
        throw new Error(apiResult.error);
      }

      const aiResponse = apiResult.data.choices[0].message.content;

      loadingDiv.style.display = 'none';
      responseTextarea.style.display = 'block';
      responseTextarea.value = aiResponse;

      // Remove any existing loading notification
      const loadingNotification = document.querySelector('.bolt-error-notification-loading');
      if (loadingNotification) {
        loadingNotification.remove();
      }

      // Show success notification
      this.showNotification('AI response received successfully!', 'success');

    } catch (error) {
      loadingDiv.style.display = 'block';
      loadingDiv.innerHTML = `
        <div style="color: #dc3545; font-weight: bold;">❌ Error: ${error.message}</div>
        <div style="margin-top: 12px; font-size: 13px; color: #666; line-height: 1.4;">
          Please check your API configuration in the extension options. Make sure your API key is valid and the service is accessible.
        </div>
        <div style="margin-top: 8px; font-size: 12px; color: #999;">
          Click "Retry" to try again.
        </div>
      `;
      responseTextarea.style.display = 'none';

      // Remove any existing loading notification
      const loadingNotification = document.querySelector('.bolt-error-notification-loading');
      if (loadingNotification) {
        loadingNotification.remove();
      }

      // Show error notification
      this.showNotification(`Failed to get AI response: ${error.message}`, 'error');

      console.error('AI API Error:', error);
    }
  }
}

// Initialize the extension
new BoltErrorAssistant();
