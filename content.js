class BoltErrorAssistant {
  constructor() {
    this.errors = [];
    this.errorButton = null;
    this.isObserving = false;
    this.init();
  }

  async init() {
    // Wait for the page to load and find the target button
    this.waitForChatWindow();
    this.startErrorObserver();
  }

  waitForChatWindow() {
    const checkForButton = () => {
      const targetButton = document.querySelector('button[data-state="closed"] span.i-ph\\:flask-duotone');
      if (targetButton && targetButton.closest('button')) {
        this.createErrorButton(targetButton.closest('button'));
      } else {
        setTimeout(checkForButton, 1000);
      }
    };
    checkForButton();
  }

  createErrorButton(referenceButton) {
    if (this.errorButton) return; // Already created

    const errorButton = document.createElement('button');
    errorButton.className = referenceButton.className;
    errorButton.type = 'button';
    errorButton.setAttribute('data-state', 'closed');
    errorButton.setAttribute('title', 'Error Assistant');
    
    errorButton.innerHTML = `
      <span class="bolt-error-icon flex items-center justify-center h-4 text-base leading-4">⚠️</span>
      <span class="truncate bolt-error-count">0</span>
    `;

    errorButton.addEventListener('click', () => this.handleErrorClick());

    // Insert after the reference button
    referenceButton.parentNode.insertBefore(errorButton, referenceButton.nextSibling);
    this.errorButton = errorButton;
  }

  startErrorObserver() {
    if (this.isObserving) return;
    
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const errorElement = node.querySelector('._PreviewError_1lprf_1') || 
                               (node.classList && node.classList.contains('_PreviewError_1lprf_1') ? node : null);
            
            if (errorElement) {
              this.captureError(errorElement);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.isObserving = true;

    // Also check for existing errors
    this.checkExistingErrors();
  }

  checkExistingErrors() {
    const existingErrors = document.querySelectorAll('._PreviewError_1lprf_1');
    existingErrors.forEach(error => this.captureError(error));
  }

  captureError(errorElement) {
    const errorText = errorElement.textContent || errorElement.innerText;
    if (errorText && !this.errors.includes(errorText)) {
      this.errors.push(errorText);
      this.updateErrorButton();
    }
  }

  updateErrorButton() {
    if (!this.errorButton) return;

    const countSpan = this.errorButton.querySelector('.bolt-error-count');
    const iconSpan = this.errorButton.querySelector('.bolt-error-icon');
    
    if (countSpan) {
      countSpan.textContent = this.errors.length;
    }
    
    if (iconSpan && this.errors.length > 0) {
      iconSpan.style.color = '#ff4444';
      iconSpan.style.animation = 'pulse 1s infinite';
    }
  }

  async handleErrorClick() {
    if (this.errors.length === 0) {
      alert('No errors captured yet.');
      return;
    }

    const latestError = this.errors[this.errors.length - 1];
    
    // Copy error to clipboard
    try {
      await navigator.clipboard.writeText(latestError);
    } catch (err) {
      console.warn('Could not copy to clipboard:', err);
    }

    // Toggle to discuss mode
    this.toggleDiscussMode();

    // Wait a bit then paste the error
    setTimeout(() => {
      this.pasteErrorToChat(latestError);
      
      // After pasting, show the AI assistant popup
      setTimeout(() => {
        this.showAIAssistantPopup(latestError);
      }, 1000);
    }, 500);
  }

  toggleDiscussMode() {
    const discussButton = document.querySelector('button[aria-label="Toggle mode"]');
    if (discussButton) {
      discussButton.click();
    }
  }

  pasteErrorToChat(errorText) {
    const textarea = document.querySelector('textarea[placeholder*="chat"], textarea[placeholder*="message"]');
    if (textarea) {
      textarea.value = errorText;
      textarea.dispatchEvent(new Event('input', { bubbles: true }));
      textarea.focus();
    }
  }

  async showAIAssistantPopup(errorText) {
    // Create popup overlay
    const overlay = document.createElement('div');
    overlay.className = 'bolt-error-popup-overlay';
    
    const popup = document.createElement('div');
    popup.className = 'bolt-error-popup';
    
    popup.innerHTML = `
      <div class="bolt-error-popup-header">
        <h3>AI Error Assistant</h3>
        <button class="bolt-error-popup-close">×</button>
      </div>
      <div class="bolt-error-popup-content">
        <label>Error Message:</label>
        <textarea class="bolt-error-input" readonly>${errorText}</textarea>
        
        <label>AI Response:</label>
        <div class="bolt-error-response-container">
          <div class="bolt-error-loading">Getting AI assistance...</div>
          <textarea class="bolt-error-response" readonly style="display: none;"></textarea>
        </div>
        
        <div class="bolt-error-popup-actions">
          <button class="bolt-error-retry">Retry</button>
          <button class="bolt-error-copy-response">Copy Response</button>
        </div>
      </div>
    `;

    overlay.appendChild(popup);
    document.body.appendChild(overlay);

    // Event listeners
    overlay.querySelector('.bolt-error-popup-close').addEventListener('click', () => {
      document.body.removeChild(overlay);
    });

    overlay.querySelector('.bolt-error-retry').addEventListener('click', () => {
      this.getAIResponse(errorText, overlay);
    });

    overlay.querySelector('.bolt-error-copy-response').addEventListener('click', () => {
      const response = overlay.querySelector('.bolt-error-response').value;
      navigator.clipboard.writeText(response);
    });

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay);
      }
    });

    // Get AI response
    this.getAIResponse(errorText, overlay);
  }

  async getAIResponse(errorText, overlay) {
    const loadingDiv = overlay.querySelector('.bolt-error-loading');
    const responseTextarea = overlay.querySelector('.bolt-error-response');
    
    loadingDiv.style.display = 'block';
    responseTextarea.style.display = 'none';

    try {
      // Get API settings from storage
      const result = await chrome.storage.local.get(['apiUrl', 'apiKey']);
      const apiUrl = result.apiUrl || 'https://gab.ai/v1';
      const apiKey = result.apiKey || '';

      const response = await fetch(`${apiUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: 'arya',
          messages: [
            {
              role: 'system',
              content: 'You are an expert developer assistant. Analyze the error message and provide a detailed explanation of the problem and step-by-step solution.'
            },
            {
              role: 'user',
              content: `I encountered this error in bolt.new: ${errorText}\n\nPlease explain what this error means and provide a detailed solution with steps to fix it.`
            }
          ],
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const aiResponse = data.choices[0].message.content;

      loadingDiv.style.display = 'none';
      responseTextarea.style.display = 'block';
      responseTextarea.value = aiResponse;

    } catch (error) {
      loadingDiv.textContent = `Error: ${error.message}`;
      console.error('AI API Error:', error);
    }
  }
}

// Initialize the extension
new BoltErrorAssistant();
