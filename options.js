document.addEventListener('DOMContentLoaded', async () => {
  const apiUrlInput = document.getElementById('apiUrl');
  const apiKeyInput = document.getElementById('apiKey');
  const saveButton = document.getElementById('save');
  const statusDiv = document.getElementById('status');

  // Load saved settings
  const result = await chrome.storage.local.get(['apiUrl', 'apiKey']);
  if (result.apiUrl) {
    apiUrlInput.value = result.apiUrl;
  }
  if (result.apiKey) {
    apiKeyInput.value = result.apiKey;
  }

  saveButton.addEventListener('click', async () => {
    const apiUrl = apiUrlInput.value.trim() || 'https://gab.ai/v1';
    const apiKey = apiKeyInput.value.trim();

    try {
      await chrome.storage.local.set({
        apiUrl: apiUrl,
        apiKey: apiKey
      });

      statusDiv.className = 'status success';
      statusDiv.textContent = 'Settings saved successfully!';
      
      setTimeout(() => {
        statusDiv.textContent = '';
        statusDiv.className = '';
      }, 3000);
    } catch (error) {
      statusDiv.className = 'status error';
      statusDiv.textContent = 'Error saving settings!';
    }
  });
});
