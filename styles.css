.bolt-error-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.bolt-error-popup {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.bolt-error-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.bolt-error-popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.bolt-error-popup-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.bolt-error-popup-close:hover {
  background: #e9ecef;
}

.bolt-error-popup-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(80vh - 140px);
}

.bolt-error-popup-content label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.bolt-error-input,
.bolt-error-response {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: monospace;
  font-size: 14px;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.bolt-error-response-container {
  position: relative;
}

.bolt-error-loading {
  background: #f8f9fa;
  padding: 20px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 16px;
}

.bolt-error-response {
  min-height: 200px;
}

.bolt-error-popup-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.bolt-error-retry,
.bolt-error-copy-response {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.bolt-error-retry:hover,
.bolt-error-copy-response:hover {
  background: #f8f9fa;
}

.bolt-error-retry {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.bolt-error-retry:hover {
  background: #0056b3;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
