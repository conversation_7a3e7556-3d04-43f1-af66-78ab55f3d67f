// Test script to verify background script is working
// Run this in the extension's service worker console

console.log('Testing background script API call...');

// Test the API call directly
const testApiCall = async () => {
  try {
    const response = await fetch('https://gab.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer gab_19b4be24e684bee9ea215ff0a2593100'
      },
      body: JSON.stringify({
        model: 'gab-ai',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant.'
          },
          {
            role: 'user',
            content: 'Hello, how are you?'
          }
        ],
        max_tokens: 100
      })
    });

    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('API Response:', data);
      console.log('✅ Background script API call successful!');
    } else {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
    }
  } catch (error) {
    console.error('❌ Fetch Error:', error);
  }
};

// Run the test
testApiCall();
